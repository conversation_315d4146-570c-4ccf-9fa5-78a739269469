import { useEffect, useState, memo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/Card';
import { Badge } from './ui/Badge';
import { Activity, Zap, Clock, Database } from 'lucide-react';

interface PerformanceMetrics {
  navigationTiming?: PerformanceNavigationTiming;
  paintTiming?: PerformancePaintTiming[];
  resourceTiming?: PerformanceResourceTiming[];
  memoryInfo?: any;
}

const PerformanceMonitor = memo(() => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({});
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Only show in development
    if (process.env.NODE_ENV !== 'development') return;

    const collectMetrics = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const paint = performance.getEntriesByType('paint') as PerformancePaintTiming[];
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      
      // @ts-ignore - Chrome specific
      const memory = (performance as any).memory;

      setMetrics({
        navigationTiming: navigation,
        paintTiming: paint,
        resourceTiming: resources,
        memoryInfo: memory,
      });
    };

    // Collect metrics after page load
    if (document.readyState === 'complete') {
      collectMetrics();
    } else {
      window.addEventListener('load', collectMetrics);
    }

    // Keyboard shortcut to toggle visibility (Ctrl+Shift+P)
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'P') {
        setIsVisible(prev => !prev);
      }
    };

    document.addEventListener('keydown', handleKeyPress);

    return () => {
      window.removeEventListener('load', collectMetrics);
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, []);

  if (process.env.NODE_ENV !== 'development' || !isVisible) {
    return null;
  }

  const { navigationTiming, paintTiming, resourceTiming, memoryInfo } = metrics;

  const getPerformanceColor = (value: number, thresholds: [number, number]) => {
    if (value < thresholds[0]) return 'bg-green-500';
    if (value < thresholds[1]) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (ms: number) => `${Math.round(ms)}ms`;

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm">
      <Card className="bg-black/90 text-white border-gray-700">
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center gap-2">
            <Activity className="w-4 h-4" />
            Performance Monitor
            <Badge variant="outline" className="text-xs">
              Ctrl+Shift+P to toggle
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-xs">
          {/* Navigation Timing */}
          {navigationTiming && (
            <div>
              <div className="flex items-center gap-1 mb-1">
                <Clock className="w-3 h-3" />
                <span className="font-medium">Page Load</span>
              </div>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <span className="text-gray-400">DOM Ready:</span>
                  <Badge 
                    className={`ml-1 text-xs ${getPerformanceColor(
                      navigationTiming.domContentLoadedEventEnd - navigationTiming.navigationStart,
                      [1000, 3000]
                    )}`}
                  >
                    {formatTime(navigationTiming.domContentLoadedEventEnd - navigationTiming.navigationStart)}
                  </Badge>
                </div>
                <div>
                  <span className="text-gray-400">Load Complete:</span>
                  <Badge 
                    className={`ml-1 text-xs ${getPerformanceColor(
                      navigationTiming.loadEventEnd - navigationTiming.navigationStart,
                      [2000, 5000]
                    )}`}
                  >
                    {formatTime(navigationTiming.loadEventEnd - navigationTiming.navigationStart)}
                  </Badge>
                </div>
              </div>
            </div>
          )}

          {/* Paint Timing */}
          {paintTiming && paintTiming.length > 0 && (
            <div>
              <div className="flex items-center gap-1 mb-1">
                <Zap className="w-3 h-3" />
                <span className="font-medium">Paint Timing</span>
              </div>
              <div className="space-y-1">
                {paintTiming.map((paint, index) => (
                  <div key={index} className="flex justify-between">
                    <span className="text-gray-400">{paint.name}:</span>
                    <Badge 
                      className={`text-xs ${getPerformanceColor(paint.startTime, [100, 300])}`}
                    >
                      {formatTime(paint.startTime)}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Memory Info */}
          {memoryInfo && (
            <div>
              <div className="flex items-center gap-1 mb-1">
                <Database className="w-3 h-3" />
                <span className="font-medium">Memory Usage</span>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-gray-400">Used:</span>
                  <Badge className="text-xs bg-blue-500">
                    {formatBytes(memoryInfo.usedJSHeapSize)}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Total:</span>
                  <Badge className="text-xs bg-gray-600">
                    {formatBytes(memoryInfo.totalJSHeapSize)}
                  </Badge>
                </div>
              </div>
            </div>
          )}

          {/* Resource Count */}
          {resourceTiming && (
            <div>
              <div className="flex justify-between">
                <span className="text-gray-400">Resources Loaded:</span>
                <Badge className="text-xs bg-purple-500">
                  {resourceTiming.length}
                </Badge>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
});

PerformanceMonitor.displayName = 'PerformanceMonitor';

export default PerformanceMonitor;
